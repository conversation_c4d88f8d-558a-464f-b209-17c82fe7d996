#!/usr/bin/env python3
"""
Test the regex patterns used for multi-file detection.
"""

import re

def test_multi_file_patterns():
    """Test the regex patterns against various multi-file requests."""
    
    # The patterns from the agent code
    multi_file_patterns = [
        r'create\s+(\d+|one|two|three|four|five|six|seven|eight|nine|ten)\s+(.+?)\s*files?(?:\s+each\s+with\s+(\d+)\s+lines?)?(?:\s+named\s+(.+))?',
        r'(\d+|one|two|three|four|five|six|seven|eight|nine|ten)\s+(.+?)\s*files?(?:\s+each\s+with\s+(\d+)\s+lines?)?(?:\s+named\s+(.+))?',
        r'create\s+(.+?)\s*files?\s*-\s*(\d+|one|two|three|four|five|six|seven|eight|nine|ten)(?:\s+each\s+with\s+(\d+)\s+lines?)?(?:\s+named\s+(.+))?'
    ]
    
    # Test cases
    test_goals = [
        "Create 3 Python files, each containing 50 lines of functional code.",
        "Create 5 Python files, each containing 300 lines of functional code.",
        "Generate 3 files with 100 lines each",
        "Make 5 Python files",
        "Create three Python files",
        "Generate five files each with 200 lines",
        "Create multiple files",
        "Create several files"
    ]
    
    print("Testing multi-file regex patterns:")
    print("=" * 60)
    
    for goal in test_goals:
        print(f"\nGoal: '{goal}'")
        
        multi_file_match = None
        matched_pattern_index = None
        
        for i, pattern in enumerate(multi_file_patterns):
            multi_file_match = re.search(pattern, goal, re.IGNORECASE)
            if multi_file_match:
                matched_pattern_index = i
                break
        
        if multi_file_match:
            print(f"  ✅ MATCHED pattern {matched_pattern_index + 1}")
            print(f"     Groups: {multi_file_match.groups()}")
            
            # Extract information like the agent does
            groups = multi_file_match.groups()
            if matched_pattern_index == 0:  # Pattern 1
                num_str, file_description_base, lines_str, naming_pattern = groups
            elif matched_pattern_index == 1:  # Pattern 2
                num_str, file_description_base, lines_str, naming_pattern = groups
            elif matched_pattern_index == 2:  # Pattern 3
                file_description_base, num_str, lines_str, naming_pattern = groups
            
            print(f"     Number: {num_str}")
            print(f"     Description: {file_description_base}")
            print(f"     Lines: {lines_str}")
            print(f"     Naming: {naming_pattern}")
        else:
            print(f"  ❌ NO MATCH")
            
            # Check fallback indicators
            multi_indicators = [r'multiple\s+files?', r'several\s+files?', r'many\s+files?', r'\d+\s*x\s*files?']
            fallback_match = False
            for indicator in multi_indicators:
                if re.search(indicator, goal, re.IGNORECASE):
                    print(f"     But matches fallback indicator: {indicator}")
                    fallback_match = True
                    break
            
            if not fallback_match:
                print(f"     No fallback match either")

if __name__ == "__main__":
    test_multi_file_patterns()
