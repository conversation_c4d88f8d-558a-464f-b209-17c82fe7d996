#!/usr/bin/env python3
"""
Test script to validate the improved large file generation capabilities.
This script tests the MindLink agent's ability to generate files of various sizes.
"""

import os
import sys
import time
import tempfile
import shutil
from pathlib import Path

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from mindlink.tools.file_tools import GenerateLargeFileTool
    from mindlink.models.openrouter import OpenRouterModel
    print("✅ Successfully imported MindLink components")
except ImportError as e:
    print(f"❌ Failed to import MindLink components: {e}")
    sys.exit(1)

def test_large_file_generation():
    """Test the large file generation tool with various file sizes."""
    print("\n" + "="*60)
    print("TESTING LARGE FILE GENERATION CAPABILITIES")
    print("="*60)
    
    # Check for API keys
    openrouter_key = os.getenv("OPENROUTER_API_KEY")

    if not openrouter_key:
        print("❌ No OpenRouter API key found. Please set OPENROUTER_API_KEY")
        return False

    # Initialize LLM (using OpenRouter only for now)
    try:
        print("🔗 Using OpenRouter for testing")
    except Exception as e:
        print(f"❌ Failed to initialize LLM: {e}")
        return False
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary test directory: {temp_dir}")
        
        # Test cases with different file sizes
        test_cases = [
            {
                "name": "Small File (100 lines)",
                "target_lines": 100,
                "description": "A simple Python calculator with basic arithmetic operations",
                "expected_success": True
            },
            {
                "name": "Medium File (500 lines)",
                "target_lines": 500,
                "description": "A comprehensive web scraper with error handling, logging, and data processing",
                "expected_success": True
            },
            {
                "name": "Large File (1500 lines)",
                "target_lines": 1500,
                "description": "A complete REST API server with authentication, database integration, and comprehensive error handling",
                "expected_success": True
            },
            {
                "name": "Very Large File (5000 lines)",
                "target_lines": 5000,
                "description": "A full-featured machine learning pipeline with data preprocessing, model training, evaluation, and deployment capabilities",
                "expected_success": True
            },
            {
                "name": "Extreme File (10000 lines)",
                "target_lines": 10000,
                "description": "A comprehensive enterprise application with microservices architecture, including user management, payment processing, inventory system, and reporting dashboard",
                "expected_success": False  # This might fail due to LLM limitations
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 Test {i}/{len(test_cases)}: {test_case['name']}")
            print(f"   Target: {test_case['target_lines']} lines")
            print(f"   Description: {test_case['description'][:80]}...")
            
            # Create test file path
            test_file = os.path.join(temp_dir, f"test_file_{test_case['target_lines']}_lines.py")
            
            # Initialize the tool
            tool = GenerateLargeFileTool()
            
            # Record start time
            start_time = time.time()
            
            try:
                # Execute the tool
                result = tool.execute(
                    path=test_file,
                    content_description=test_case['description'],
                    target_line_count=test_case['target_lines'],
                    max_chunks=100,  # Allow plenty of chunks
                    context_carryover_lines=50
                )
                
                execution_time = time.time() - start_time
                
                # Check results
                if result.get('status') == 'success':
                    # Verify file exists and count lines
                    if os.path.exists(test_file):
                        with open(test_file, 'r', encoding='utf-8') as f:
                            actual_lines = len(f.readlines())
                        
                        success_rate = (actual_lines / test_case['target_lines']) * 100
                        
                        print(f"   ✅ SUCCESS: Generated {actual_lines} lines ({success_rate:.1f}% of target)")
                        print(f"   ⏱️  Execution time: {execution_time:.2f} seconds")
                        
                        results.append({
                            'test': test_case['name'],
                            'target_lines': test_case['target_lines'],
                            'actual_lines': actual_lines,
                            'success_rate': success_rate,
                            'execution_time': execution_time,
                            'status': 'success'
                        })
                    else:
                        print(f"   ❌ FAILED: File was not created")
                        results.append({
                            'test': test_case['name'],
                            'target_lines': test_case['target_lines'],
                            'actual_lines': 0,
                            'success_rate': 0,
                            'execution_time': execution_time,
                            'status': 'file_not_created'
                        })
                else:
                    print(f"   ❌ FAILED: {result.get('observation', 'Unknown error')}")
                    results.append({
                        'test': test_case['name'],
                        'target_lines': test_case['target_lines'],
                        'actual_lines': 0,
                        'success_rate': 0,
                        'execution_time': execution_time,
                        'status': 'tool_error',
                        'error': result.get('observation', 'Unknown error')
                    })
                    
            except Exception as e:
                execution_time = time.time() - start_time
                print(f"   ❌ EXCEPTION: {e}")
                results.append({
                    'test': test_case['name'],
                    'target_lines': test_case['target_lines'],
                    'actual_lines': 0,
                    'success_rate': 0,
                    'execution_time': execution_time,
                    'status': 'exception',
                    'error': str(e)
                })
        
        # Print summary
        print("\n" + "="*60)
        print("TEST RESULTS SUMMARY")
        print("="*60)
        
        successful_tests = [r for r in results if r['status'] == 'success']
        total_tests = len(results)
        
        print(f"📊 Overall Success Rate: {len(successful_tests)}/{total_tests} ({(len(successful_tests)/total_tests)*100:.1f}%)")
        print()
        
        for result in results:
            status_emoji = "✅" if result['status'] == 'success' else "❌"
            print(f"{status_emoji} {result['test']}")
            print(f"   Target: {result['target_lines']} lines | Actual: {result['actual_lines']} lines | Rate: {result['success_rate']:.1f}%")
            print(f"   Time: {result['execution_time']:.2f}s | Status: {result['status']}")
            if 'error' in result:
                print(f"   Error: {result['error'][:100]}...")
            print()
        
        # Determine if improvements are working
        large_file_tests = [r for r in results if r['target_lines'] >= 1500]
        large_file_success = [r for r in large_file_tests if r['success_rate'] >= 70]
        
        if len(large_file_success) > 0:
            print("🎉 IMPROVEMENT VALIDATION: Large file generation is working!")
            print(f"   Successfully generated {len(large_file_success)}/{len(large_file_tests)} large files (≥1500 lines)")
            return True
        else:
            print("⚠️  IMPROVEMENT NEEDED: Large file generation still has issues")
            print(f"   Only {len(large_file_success)}/{len(large_file_tests)} large files succeeded")
            return False

if __name__ == "__main__":
    success = test_large_file_generation()
    sys.exit(0 if success else 1)
