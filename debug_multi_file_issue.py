#!/usr/bin/env python3
"""
Debug script to understand why multi-file creation only creates one file.
This script will test the agent's multi-file creation capabilities step by step.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from mindlink.agent import AgentOS
    from mindlink.models.openrouter import OpenRouterModel
    from mindlink.config import DEFAULT_SYSTEM_PROMPT
    print("✅ Successfully imported MindLink components")
except ImportError as e:
    print(f"❌ Failed to import MindLink components: {e}")
    sys.exit(1)

def debug_multi_file_creation():
    """Debug the multi-file creation process."""
    print("\n" + "="*60)
    print("DEBUGGING MULTI-FILE CREATION ISSUE")
    print("="*60)
    
    # Check for API keys
    openrouter_key = os.getenv("OPENROUTER_API_KEY")
    
    if not openrouter_key:
        print("❌ No OpenRouter API key found. Please set OPENROUTER_API_KEY")
        return False

    # Initialize LLM and Agent
    try:
        llm = OpenRouterModel(api_key=openrouter_key, model_name="mistral-small-3.1")
        agent = AgentOS(llm=llm, system_prompt_template=DEFAULT_SYSTEM_PROMPT)
        print("🔗 Successfully initialized agent with OpenRouter")
    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}")
        return False
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary test directory: {temp_dir}")
        os.chdir(temp_dir)
        
        # Test case: Simple multi-file request
        test_goal = "Create 3 Python files, each containing 50 lines of functional code."
        
        print(f"\n🎯 Testing goal: {test_goal}")
        
        # Step 1: Test planning phase
        print("\n📋 Step 1: Testing planning phase...")
        try:
            # First, let's test the regex patterns manually
            import re
            multi_file_patterns = [
                r'create\s+(\d+|one|two|three|four|five|six|seven|eight|nine|ten)\s+(.+?)\s*files?(?:\s+each\s+with\s+(\d+)\s+lines?)?(?:\s+named\s+(.+))?',
                r'(\d+|one|two|three|four|five|six|seven|eight|nine|ten)\s+(.+?)\s*files?(?:\s+each\s+with\s+(\d+)\s+lines?)?(?:\s+named\s+(.+))?',
                r'create\s+(.+?)\s*files?\s*-\s*(\d+|one|two|three|four|five|six|seven|eight|nine|ten)(?:\s+each\s+with\s+(\d+)\s+lines?)?(?:\s+named\s+(.+))?'
            ]

            print(f"   Testing regex patterns against: '{test_goal}'")
            multi_file_match = None
            for i, pattern in enumerate(multi_file_patterns):
                match = re.search(pattern, test_goal, re.IGNORECASE)
                if match:
                    print(f"   ✅ Pattern {i+1} matched: {match.groups()}")
                    multi_file_match = match
                    break
                else:
                    print(f"   ❌ Pattern {i+1} no match")

            if not multi_file_match:
                print(f"   ❌ No regex patterns matched!")
            else:
                # Test the logic that should create the context
                groups = multi_file_match.groups()
                print(f"   📊 Testing context creation logic:")
                print(f"      Groups: {groups}")

                if len(groups) >= 2:
                    num_files_str = groups[0].lower() if groups[0] else '1'
                    file_description_base = groups[1].strip() if groups[1] else 'python file'
                    lines_str = groups[2] if len(groups) > 2 and groups[2] else None
                    naming_pattern = groups[3] if len(groups) > 3 and groups[3] else None

                    print(f"      num_files_str: '{num_files_str}'")
                    print(f"      file_description_base: '{file_description_base}'")
                    print(f"      lines_str: {lines_str}")
                    print(f"      naming_pattern: {naming_pattern}")

                    num_map = {'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5, 'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10}
                    try:
                        num_files = int(num_files_str) if num_files_str.isdigit() else num_map.get(num_files_str, 1)
                        print(f"      num_files: {num_files}")

                        if num_files >= 2 and num_files <= 20:
                            print(f"      ✅ Should create context (2 <= {num_files} <= 20)")
                            if not file_description_base or len(file_description_base.strip()) < 3:
                                print(f"      ❌ Description too short: '{file_description_base}'")
                            else:
                                print(f"      ✅ Description valid: '{file_description_base}'")
                        else:
                            print(f"      ❌ File count outside range: {num_files}")
                    except ValueError as e:
                        print(f"      ❌ ValueError: {e}")
                else:
                    print(f"      ❌ Not enough groups: {len(groups)}")

            # Add debug logging to the agent to see what's happening
            import logging
            logging.basicConfig(level=logging.DEBUG)

            # Monkey patch the agent to add debug logging
            original_plan_once = agent.plan_once
            def debug_plan_once(goal):
                print(f"   🔍 DEBUG: Starting plan_once with goal: '{goal}'")

                # Check if multi-file context exists before planning
                print(f"   🔍 DEBUG: Multi-file context before planning: {agent._multi_file_request_context}")

                result = original_plan_once(goal)

                # Check if multi-file context exists after planning
                print(f"   🔍 DEBUG: Multi-file context after planning: {agent._multi_file_request_context}")

                return result

            agent.plan_once = debug_plan_once

            plan = agent.plan_once(test_goal)
            print(f"\n   Generated plan with {len(plan)} actions:")

            for i, action in enumerate(plan, 1):
                print(f"   {i}. {action.tool_name}")
                if hasattr(action, 'parameters') and action.parameters:
                    if 'path' in action.parameters:
                        print(f"      Path: {action.parameters['path']}")
                    if 'content_description' in action.parameters:
                        desc = action.parameters['content_description'][:50] + "..." if len(action.parameters['content_description']) > 50 else action.parameters['content_description']
                        print(f"      Description: {desc}")

            # Check if multi-file context was created
            if hasattr(agent, '_multi_file_request_context') and agent._multi_file_request_context:
                ctx = agent._multi_file_request_context
                print(f"\n   ✅ Multi-file context created:")
                print(f"      Files remaining: {ctx.get('num_files_remaining', 'N/A')}")
                print(f"      Files created count: {ctx.get('files_created_count', 'N/A')}")
                print(f"      Description base: {ctx.get('file_description_base', 'N/A')}")
                print(f"      Lines per file: {ctx.get('lines_per_file', 'N/A')}")
            else:
                print(f"\n   ❌ No multi-file context created")
                print(f"      Context attribute exists: {hasattr(agent, '_multi_file_request_context')}")
                if hasattr(agent, '_multi_file_request_context'):
                    print(f"      Context value: {agent._multi_file_request_context}")
                
        except Exception as e:
            print(f"   ❌ Planning failed: {e}")
            return False
        
        # Step 2: Test execution phase
        print(f"\n⚡ Step 2: Testing execution phase...")
        try:
            response = agent.batch_execute_plan(plan)
            print(f"   Execution status: {response.status}")
            print(f"   Execution result: {response.observation[:200]}...")
            
            # Check what files were actually created
            created_files = [f for f in os.listdir(temp_dir) if f.endswith('.py')]
            print(f"\n   📁 Files created: {len(created_files)}")
            for file in created_files:
                file_path = os.path.join(temp_dir, file)
                with open(file_path, 'r') as f:
                    lines = len(f.readlines())
                print(f"      {file}: {lines} lines")
                
        except Exception as e:
            print(f"   ❌ Execution failed: {e}")
            return False
        
        # Step 3: Analyze the issue
        print(f"\n🔍 Step 3: Issue analysis...")
        
        expected_files = 3
        actual_files = len(created_files)
        
        if actual_files == expected_files:
            print(f"   ✅ SUCCESS: Created {actual_files}/{expected_files} files as expected")
            return True
        elif actual_files == 1:
            print(f"   ❌ ISSUE CONFIRMED: Only created {actual_files}/{expected_files} files")
            print(f"   🔍 This confirms the reported issue - only one file is being created")
            
            # Let's examine the plan more closely
            create_file_actions = [a for a in plan if a.tool_name == 'create_file']
            print(f"   📋 Plan had {len(create_file_actions)} create_file actions")
            
            if len(create_file_actions) < expected_files:
                print(f"   🚨 ROOT CAUSE: Planning phase only generated {len(create_file_actions)} create_file actions")
                print(f"   💡 The issue is in the planning logic, not execution")
            else:
                print(f"   🚨 ROOT CAUSE: Planning generated correct actions but execution stopped early")
                print(f"   💡 The issue is in the execution logic")
                
            return False
        else:
            print(f"   ⚠️  PARTIAL SUCCESS: Created {actual_files}/{expected_files} files")
            return False

if __name__ == "__main__":
    success = debug_multi_file_creation()
    sys.exit(0 if success else 1)
