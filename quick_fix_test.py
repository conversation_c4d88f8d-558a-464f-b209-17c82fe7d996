#!/usr/bin/env python3
"""
Quick test to verify our fixes are working.
"""

import os
import sys

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from mindlink.agent import Agent<PERSON>
    from mindlink.models.openrouter import OpenRouterModel
    from mindlink.config import DEFAULT_SYSTEM_PROMPT
    print("✅ Successfully imported MindLink components")
except ImportError as e:
    print(f"❌ Failed to import MindLink components: {e}")
    sys.exit(1)

def quick_test():
    """Quick test of our fixes."""
    print("\n" + "="*50)
    print("QUICK MULTI-FILE FIXES TEST")
    print("="*50)
    
    # Check for API keys
    openrouter_key = os.getenv("OPENROUTER_API_KEY")
    
    if not openrouter_key:
        print("❌ No OpenRouter API key found. Please set OPENROUTER_API_KEY")
        return False

    # Initialize LLM and Agent
    try:
        llm = OpenRouterModel(api_key=openrouter_key, model_name="mistral-small-3.1")
        agent = AgentOS(llm=llm, system_prompt_template=DEFAULT_SYSTEM_PROMPT)
        print("🔗 Successfully initialized agent")
    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}")
        return False
    
    # Test just the planning phase first
    test_goal = "Create 3 Python files, each containing exactly 20 lines of code"
    
    print(f"\n🎯 Testing goal: {test_goal}")
    
    try:
        print("\n📋 Testing planning phase...")
        plan = agent.plan_once(test_goal)
        
        print(f"   Generated {len(plan)} actions:")
        create_file_count = 0
        for i, action in enumerate(plan, 1):
            print(f"   {i}. {action.tool_name}")
            if action.tool_name == 'create_file':
                create_file_count += 1
                target_lines = action.parameters.get('target_line_count', 'Not specified')
                print(f"      Target lines: {target_lines}")
        
        print(f"\n📊 Planning Results:")
        print(f"   create_file actions: {create_file_count}/3")
        print(f"   Planning success: {'✅' if create_file_count == 3 else '❌'}")
        
        if create_file_count == 3:
            print(f"\n🎉 FILE COUNT FIX WORKING!")
            return True
        else:
            print(f"\n⚠️ File count fix needs more work")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = quick_test()
    sys.exit(0 if success else 1)
